import { useState, useEffect } from 'react'
import Head from 'next/head'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import ServiceTileGrid from '@/components/admin/pos/ServiceTileGrid'
import POSCalendarView from '@/components/admin/pos/POSCalendarView'
import POSCheckout from '@/components/admin/pos/POSCheckout'
import AvailabilityDashboard from '@/components/admin/pos/AvailabilityDashboard'
import SquareDebugger from '@/components/admin/pos/SquareDebugger'
import POSProductionDebugger from '@/components/admin/pos/POSProductionDebugger'
import { supabase } from '@/lib/supabase'
import { safeRender } from '@/lib/safe-render-utils'
import { startPOSSessionMonitoring, stopPOSSessionMonitoring } from '@/lib/pos-session-manager'
import { initializePOSAuthProtection } from '@/lib/pos-auth-protection'
import { useAuth } from '@/contexts/AuthContext'
import '@/lib/console-filter' // Activate global console filtering
import styles from '@/styles/admin/POS.module.css'

export default function POSTerminal() {
  // ===== 3-STAGE WORKFLOW: services -> calendar -> checkout =====
  const [currentStep, setCurrentStep] = useState('services') // 'services', 'calendar', 'checkout'
  const [selectedService, setSelectedService] = useState(null)
  const [selectedTier, setSelectedTier] = useState(null)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null)
  const [selectedArtist, setSelectedArtist] = useState(null) // Set from time slot
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [dashboardStats, setDashboardStats] = useState({
    todayBookings: 0,
    availableSlots: 0,
    revenue: 0
  })

  // Get auth context at component level (not inside useEffect)
  const { loading: authLoading } = useAuth()

  // Initialize POS with auth protection and data loading
  useEffect(() => {
    console.log('🏪 POS Terminal initializing with enhanced auth protection...')

    // Initialize POS authentication protection first
    const cleanupAuthProtection = initializePOSAuthProtection()

    // Fetch services data
    fetchServices()

    // Cleanup on unmount
    return () => {
      console.log('🏪 POS Terminal cleaning up...')
      stopPOSSessionMonitoring()
      cleanupAuthProtection()
    }
  }, [])

  // Start POS session monitoring when auth context is ready
  useEffect(() => {
    if (!authLoading) {
      console.log('🔄 Starting POS session monitoring after auth context initialization...')
      startPOSSessionMonitoring()
    }
  }, [authLoading])

  const fetchServices = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🏪 Fetching services with available artists for POS Terminal...')

      // Use the new API endpoint that includes artist availability
      const response = await fetch('/api/admin/pos/services-with-artists')

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      console.log(`✅ Loaded ${data.services?.length || 0} services with artists`)
      setServices(data.services || [])

      // Also fetch dashboard stats
      fetchDashboardStats()
    } catch (err) {
      console.error('❌ Error fetching services with artists:', err)
      setError('Failed to load services. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Fetch dashboard statistics
  const fetchDashboardStats = async () => {
    try {
      console.log('📊 Fetching dashboard stats...')

      const today = new Date().toISOString().split('T')[0]

      // Fetch today's bookings
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select('total_amount, status')
        .gte('created_at', `${today}T00:00:00`)
        .lt('created_at', `${today}T23:59:59`)

      if (bookingsError) {
        console.error('Error fetching bookings:', bookingsError)
        return
      }

      // Calculate stats
      const todayBookings = bookingsData?.length || 0
      const revenue = bookingsData?.reduce((sum, booking) => {
        return sum + (parseFloat(booking.total_amount) || 0)
      }, 0) || 0

      // Mock available slots calculation (would be based on actual availability logic)
      const availableSlots = Math.max(0, 20 - todayBookings)

      setDashboardStats({
        todayBookings,
        availableSlots,
        revenue
      })

      console.log('✅ Dashboard stats updated:', { todayBookings, availableSlots, revenue })
    } catch (error) {
      console.error('❌ Error fetching dashboard stats:', error)
      // Keep default stats on error
    }
  }

  // ===== 3-STAGE WORKFLOW HANDLERS =====

  // STAGE 1: Service and Tier Selection
  const handleServiceAndTierSelect = (service, tier) => {
    console.log('🎯 Service and tier selected:', service.name, tier.name, 'Duration:', tier.duration, 'Price:', tier.price)
    setSelectedService(service)
    setSelectedTier(tier)
    setSelectedTimeSlot(null)
    setSelectedArtist(null)
    setCurrentStep('calendar')
  }

  // STAGE 2: Time Slot Selection
  const handleTimeSlotSelect = (timeSlot) => {
    console.log('📅 Time slot selected:', timeSlot)
    setSelectedTimeSlot(timeSlot)
    // Set artist from time slot if available
    if (timeSlot.artist) {
      setSelectedArtist(timeSlot.artist)
    }
    setCurrentStep('checkout')
  }

  // Navigation handlers
  const handleBackToServices = () => {
    setSelectedService(null)
    setSelectedTier(null)
    setSelectedTimeSlot(null)
    setSelectedArtist(null)
    setCurrentStep('services')
  }

  const handleBackToCalendar = () => {
    setSelectedTimeSlot(null)
    setSelectedArtist(null)
    setCurrentStep('calendar')
  }

  // STAGE 3: Transaction Complete
  const handleTransactionComplete = () => {
    // Reset to services view after successful transaction
    setSelectedService(null)
    setSelectedTier(null)
    setSelectedTimeSlot(null)
    setSelectedArtist(null)
    setCurrentStep('services')

    // Refresh dashboard stats after successful transaction
    fetchDashboardStats()
  }

  const handleQuickBooking = (timeSlot) => {
    if (timeSlot) {
      console.log('Quick booking for time slot:', timeSlot)
      // In a real implementation, this would open a quick booking modal
      // For now, we'll just log the action
      alert(`Quick booking feature would open for ${timeSlot.time}`)
    }
  }

  const handleViewCalendar = () => {
    // Open calendar in new tab
    window.open('/admin/bookings', '_blank')
  }

  // 3-STAGE STEP INDICATOR
  const renderStepIndicator = () => (
    <div className={styles.stepIndicator}>
      <div className={`${styles.step} ${currentStep === 'services' ? styles.active : ''}`}>
        <span className={styles.stepNumber}>1</span>
        <span className={styles.stepLabel}>Select Service & Duration</span>
      </div>
      <div className={`${styles.step} ${currentStep === 'calendar' ? styles.active : ''}`}>
        <span className={styles.stepNumber}>2</span>
        <span className={styles.stepLabel}>Choose Time Slot</span>
      </div>
      <div className={`${styles.step} ${currentStep === 'checkout' ? styles.active : ''}`}>
        <span className={styles.stepNumber}>3</span>
        <span className={styles.stepLabel}>Checkout & Payment</span>
      </div>
    </div>
  )

  if (loading) {
    return (
      <ProtectedRoute>
        <AdminLayout title="POS Terminal" collapseSidebar={true}>
          <div className={styles.posContainer}>
            <div className={styles.loading}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading services...</p>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  if (error) {
    return (
      <ProtectedRoute>
        <AdminLayout title="POS Terminal" collapseSidebar={true}>
          <div className={styles.posContainer}>
            <div className={styles.error}>
              <h3>Error Loading POS Terminal</h3>
              <p>{error}</p>
              <button
                className={styles.retryButton}
                onClick={fetchServices}
              >
                Retry
              </button>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <Head>
        {/* Console monitoring removed for production security */}
      </Head>
      <AdminLayout title="POS Terminal" collapseSidebar={true}>
        <div className={styles.posContainer}>
          <div className={styles.posHeader}>
            <div className={styles.headerLeft}>
              <h1 className={styles.posTitle}>Point of Sale Terminal</h1>
              <p className={styles.posSubtitle}>Festival & Event Service Booking - 3-Stage Process</p>
              {renderStepIndicator()}
            </div>
            <div className={styles.headerRight}>
              <div className={styles.quickStats}>
                <div className={styles.statItem}>
                  <div className={styles.statValue}>{safeRender(dashboardStats.todayBookings, '0')}</div>
                  <div className={styles.statLabel}>Today's Bookings</div>
                </div>
                <div className={styles.statItem}>
                  <div className={styles.statValue}>{safeRender(dashboardStats.availableSlots, '0')}</div>
                  <div className={styles.statLabel}>Available Slots</div>
                </div>
                <div className={styles.statItem}>
                  <div className={styles.statValue}>${safeRender(dashboardStats.revenue.toFixed(2), '0.00')}</div>
                  <div className={styles.statLabel}>Today's Revenue</div>
                </div>
              </div>
              <button
                className={styles.refreshButton}
                onClick={fetchDashboardStats}
                title="Refresh dashboard stats"
              >
                🔄 Refresh
              </button>
            </div>
          </div>

          {/* Availability Dashboard - shown only on services step */}
          {currentStep === 'services' && (
            <AvailabilityDashboard
              onQuickBooking={handleQuickBooking}
              onViewCalendar={handleViewCalendar}
            />
          )}

          <div className={styles.posContent}>
            {/* STAGE 1: Service & Tier Selection */}
            {currentStep === 'services' && (
              <ServiceTileGrid
                services={services}
                onServiceSelect={handleServiceAndTierSelect}
                showTierSelection={true}
              />
            )}

            {/* STAGE 2: Calendar/Time Slot Selection */}
            {currentStep === 'calendar' && selectedService && selectedTier && (
              <POSCalendarView
                service={selectedService}
                tier={selectedTier}
                onTimeSlotSelect={handleTimeSlotSelect}
                onBack={handleBackToServices}
              />
            )}

            {/* STAGE 3: Checkout & Payment */}
            {currentStep === 'checkout' && selectedService && selectedTier && selectedTimeSlot && (
              <POSCheckout
                service={selectedService}
                tier={selectedTier}
                timeSlot={selectedTimeSlot}
                artist={selectedArtist}
                onBack={handleBackToCalendar}
                onComplete={handleTransactionComplete}
              />
            )}
          </div>

          {/* Debug components */}
          {process.env.NODE_ENV === 'development' && <SquareDebugger />}
          <POSProductionDebugger />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
